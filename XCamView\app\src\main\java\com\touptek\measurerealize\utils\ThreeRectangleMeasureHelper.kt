package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import java.util.*
import kotlin.math.*

/**
 * 🎯 三点矩形测量实例 - 支持旋转的专业级数据结构
 */
data class ThreeRectangleMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var centerPoint: PointF = PointF(),           // 矩形中心点（视图坐标）
    var width: Float = 0f,                        // 矩形宽度
    var height: Float = 0f,                       // 矩形高度
    var rotationAngle: Float = 0f,                // 旋转角度（弧度）
    var viewControlPoints: MutableList<PointF> = mutableListOf(), // [左上角, 右上角, 右下角] 控制点
    var bitmapCenterPoint: PointF = PointF(),     // 中心点位图坐标
    var bitmapWidth: Float = 0f,                  // 位图坐标系宽度
    var bitmapHeight: Float = 0f,                 // 位图坐标系高度
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {
    /**
     * 🎯 计算旋转后的四个角点坐标
     */
    fun calculateRotatedCorners(): List<PointF> {
        if (width <= 0 || height <= 0) return emptyList()
        
        val halfWidth = width / 2f
        val halfHeight = height / 2f
        val cos = cos(rotationAngle)
        val sin = sin(rotationAngle)
        
        // 相对于中心点的四个角点（未旋转）
        val corners = listOf(
            PointF(-halfWidth, -halfHeight), // 左上角
            PointF(halfWidth, -halfHeight),  // 右上角
            PointF(halfWidth, halfHeight),   // 右下角
            PointF(-halfWidth, halfHeight)   // 左下角
        )
        
        // 应用旋转变换并转换到绝对坐标
        return corners.map { corner ->
            val rotatedX = corner.x * cos - corner.y * sin + centerPoint.x
            val rotatedY = corner.x * sin + corner.y * cos + centerPoint.y
            PointF(rotatedX, rotatedY)
        }
    }
    
    /**
     * 🎯 计算面积（宽度 × 高度）
     */
    fun calculateArea(): Double {
        return (bitmapWidth * bitmapHeight).toDouble()
    }
    
    /**
     * 🎯 计算周长（2 × (宽度 + 高度)）
     */
    fun calculatePerimeter(): Double {
        return (2.0 * (bitmapWidth + bitmapHeight))
    }
    
    /**
     * 🎯 从中心点、尺寸、角度更新控制点位置
     */
    fun updateControlPointsFromGeometry() {
        if (width <= 0 || height <= 0) return
        
        val halfWidth = width / 2f
        val halfHeight = height / 2f
        val cos = cos(rotationAngle)
        val sin = sin(rotationAngle)
        
        viewControlPoints.clear()
        
        // 左上角控制点
        val topLeftX = -halfWidth * cos - (-halfHeight) * sin + centerPoint.x
        val topLeftY = -halfWidth * sin + (-halfHeight) * cos + centerPoint.y
        viewControlPoints.add(PointF(topLeftX, topLeftY))
        
        // 右上角控制点（旋转控制）
        val topRightX = halfWidth * cos - (-halfHeight) * sin + centerPoint.x
        val topRightY = halfWidth * sin + (-halfHeight) * cos + centerPoint.y
        viewControlPoints.add(PointF(topRightX, topRightY))
        
        // 右下角控制点
        val bottomRightX = halfWidth * cos - halfHeight * sin + centerPoint.x
        val bottomRightY = halfWidth * sin + halfHeight * cos + centerPoint.y
        viewControlPoints.add(PointF(bottomRightX, bottomRightY))
        
        updateTextPosition()
    }
    
    /**
     * 🎯 从控制点位置反推中心点、尺寸、角度
     */
    fun updateGeometryFromControlPoints(pointIndex: Int, newPoint: PointF) {
        if (viewControlPoints.size < 3) return
        
        when (pointIndex) {
            0 -> { // 左上角控制点移动 - 改变尺寸，保持右下角和角度
                val bottomRight = viewControlPoints[2]
                updateSizeFromTwoPoints(newPoint, bottomRight)
                viewControlPoints[0] = newPoint
                updateControlPointsFromGeometry()
            }
            1 -> { // 右上角控制点移动 - 改变旋转角度，保持中心和尺寸
                updateRotationFromControlPoint(newPoint)
                updateControlPointsFromGeometry()
            }
            2 -> { // 右下角控制点移动 - 改变尺寸，保持左上角和角度
                val topLeft = viewControlPoints[0]
                updateSizeFromTwoPoints(topLeft, newPoint)
                viewControlPoints[2] = newPoint
                updateControlPointsFromGeometry()
            }
        }
        
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🎯 从两个对角点更新尺寸和中心点
     */
    private fun updateSizeFromTwoPoints(point1: PointF, point2: PointF) {
        // 计算新的中心点
        centerPoint.x = (point1.x + point2.x) / 2f
        centerPoint.y = (point1.y + point2.y) / 2f
        
        // 计算新的尺寸（考虑旋转）
        val dx = point2.x - point1.x
        val dy = point2.y - point1.y
        val cos = cos(rotationAngle)
        val sin = sin(rotationAngle)
        
        // 将对角线向量转换到未旋转的坐标系
        val unrotatedDx = dx * cos + dy * sin
        val unrotatedDy = -dx * sin + dy * cos
        
        width = abs(unrotatedDx)
        height = abs(unrotatedDy)
    }
    
    /**
     * 🎯 从右上角控制点更新旋转角度
     */
    private fun updateRotationFromControlPoint(controlPoint: PointF) {
        val dx = controlPoint.x - centerPoint.x
        val dy = controlPoint.y - centerPoint.y
        
        // 计算从中心点到控制点的角度
        val angle = atan2(dy, dx)
        
        // 右上角控制点在未旋转状态下应该在 (width/2, -height/2) 位置
        // 所以其角度应该是 atan2(-height/2, width/2)
        val expectedAngle = atan2(-height / 2f, width / 2f)
        
        rotationAngle = angle - expectedAngle
        
        // 标准化角度到 [-π, π] 范围
        while (rotationAngle > PI) rotationAngle -= 2 * PI.toFloat()
        while (rotationAngle < -PI) rotationAngle += 2 * PI.toFloat()
    }
    
    /**
     * 🎯 更新文本位置 - 矩形中心
     */
    fun updateTextPosition() {
        textPosition = PointF(centerPoint.x, centerPoint.y)
    }
    
    /**
     * 🎯 检查点是否在控制点触摸范围内
     */
    fun isControlPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex !in 0 until viewControlPoints.size) return false
        
        val controlPoint = viewControlPoints[pointIndex]
        val distance = sqrt((touchPoint.x - controlPoint.x).pow(2) + (touchPoint.y - controlPoint.y).pow(2))
        return distance <= touchRadius
    }
    
    /**
     * 🎯 获取最近的控制点索引
     */
    fun getNearestControlPointIndex(touchPoint: PointF): Int {
        if (viewControlPoints.isEmpty()) return -1
        
        var nearestIndex = -1
        var minDistance = Float.MAX_VALUE
        
        viewControlPoints.forEachIndexed { index, controlPoint ->
            val distance = sqrt((touchPoint.x - controlPoint.x).pow(2) + (touchPoint.y - controlPoint.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }

    /**
     * 🔄 同步位图坐标 - 从视图坐标转换到位图坐标
     */
    fun syncBitmapCoords(imageView: TpImageView) {
        // 转换中心点
        val bitmapCenter = convertViewToBitmapCoords(centerPoint, imageView)
        bitmapCenterPoint.set(bitmapCenter.x, bitmapCenter.y)

        // 计算位图坐标系下的尺寸
        val scaleX = imageView.scaleX
        val scaleY = imageView.scaleY
        bitmapWidth = width / scaleX
        bitmapHeight = height / scaleY

        lastModified = System.currentTimeMillis()
    }

    /**
     * 🔄 同步视图坐标 - 从位图坐标转换到视图坐标
     */
    fun syncViewCoords(imageView: TpImageView) {
        // 转换中心点
        val viewCenter = convertBitmapToViewCoords(bitmapCenterPoint, imageView)
        centerPoint.set(viewCenter.x, viewCenter.y)

        // 计算视图坐标系下的尺寸
        val scaleX = imageView.scaleX
        val scaleY = imageView.scaleY
        width = bitmapWidth * scaleX
        height = bitmapHeight * scaleY

        // 更新控制点位置
        updateControlPointsFromGeometry()

        lastModified = System.currentTimeMillis()
    }

    /**
     * 🔄 坐标转换方法 - 视图坐标到位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: TpImageView): PointF {
        val matrix = Matrix()
        imageView.imageMatrix.invert(matrix)
        val point = FloatArray(2) { 0f }
        point[0] = viewPoint.x
        point[1] = viewPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * 🔄 坐标转换方法 - 位图坐标到视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }
}

/**
 * 🎯 专业级三点矩形测量助手 - 遵循现有架构模式
 */
class ThreeRectangleMeasureHelper {
    companion object {
        private const val TAG = "ThreeRectangleHelper"

        // 交互参数 - 与其他测量功能保持一致
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间
        private const val CLICK_TOLERANCE = 30f        // 点击容差

        // 默认尺寸参数
        private const val DEFAULT_RECT_SIZE = 200f     // 默认矩形尺寸
    }

    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var originalBitmap: Bitmap
    private var isInitialized = false

    // 测量数据
    private val measurements = mutableListOf<ThreeRectangleMeasurement>()
    private var selectedMeasurement: ThreeRectangleMeasurement? = null

    // 交互状态管理 - 与其他Helper保持一致
    private var isDraggingPoint = false
    private var draggedPointIndex = -1 // 0=左上角, 1=右上角(旋转), 2=右下角
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调函数
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        this.isInitialized = true

        Log.d(TAG, "🚀 ThreeRectangleMeasureHelper initialized")
    }

    /**
     * 🔄 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 🎯 在屏幕中心创建默认三点矩形测量
     */
    fun startNewMeasurement(): String {
        if (!isInitialized) {
            Log.e(TAG, "❌ Helper not initialized")
            return ""
        }

        try {
            Log.d(TAG, "🎯 Creating new three rectangle measurement")

            // 计算屏幕中心位置
            val centerX = imageView.width / 2f
            val centerY = imageView.height / 2f

            // 创建三点矩形测量实例
            val measurement = ThreeRectangleMeasurement().apply {
                centerPoint.set(centerX, centerY)
                width = DEFAULT_RECT_SIZE
                height = DEFAULT_RECT_SIZE
                rotationAngle = 0f // 初始不旋转

                isCompleted = true
                isSelected = true // 新创建的测量默认选中

                // 更新控制点位置
                updateControlPointsFromGeometry()
            }

            // 同步位图坐标
            measurement.syncBitmapCoords(imageView)

            // 取消其他测量的选中状态
            measurements.forEach {
                it.isSelected = false
                it.isEditing = false
            }

            measurements.add(measurement)
            selectedMeasurement = measurement

            measurementUpdateCallback?.invoke()
            Log.d(TAG, "✅ Three rectangle measurement created: ${measurement.id}")
            return measurement.id

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error creating three rectangle measurement: ${e.message}")
            return ""
        }
    }

    /**
     * 🎯 添加新的三点矩形测量（在测量模式下）
     */
    fun addNewMeasurement(): String {
        return startNewMeasurement()
    }

    /**
     * 🎯 处理触摸事件 - 支持三点控制逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) return false

        val touchPoint = PointF(event.x, event.y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDown(touchPoint, viewWidth, viewHeight)
            }
            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(touchPoint)
            }
            MotionEvent.ACTION_UP -> {
                return handleTouchUp(touchPoint)
            }
        }

        return false
    }

    /**
     * 🎯 处理触摸按下事件
     */
    private fun handleTouchDown(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        // 检查是否在UI保护区域
        if (!isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
            Log.d(TAG, "🛡️ Touch in UI area, ignoring")
            return false
        }

        // 检查是否触摸到控制点
        measurements.forEach { measurement ->
            val controlPointIndex = measurement.getNearestControlPointIndex(touchPoint)
            if (controlPointIndex >= 0 && measurement.isControlPointInTouchRange(touchPoint, controlPointIndex, TOUCH_RADIUS)) {
                // 选中这个测量并开始拖拽
                selectedMeasurement?.isSelected = false
                measurement.isSelected = true
                selectedMeasurement = measurement

                isDraggingPoint = true
                draggedPointIndex = controlPointIndex
                longPressStartTime = System.currentTimeMillis()
                lastTouchX = touchPoint.x
                lastTouchY = touchPoint.y

                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🎯 Started dragging control point $controlPointIndex")
                return true
            }
        }

        // 检查是否触摸到矩形内部
        measurements.forEach { measurement ->
            if (isPointInRotatedRectangle(touchPoint, measurement)) {
                // 选中这个测量
                selectedMeasurement?.isSelected = false
                measurement.isSelected = true
                selectedMeasurement = measurement

                longPressStartTime = System.currentTimeMillis()
                lastTouchX = touchPoint.x
                lastTouchY = touchPoint.y

                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🎯 Selected three rectangle measurement: ${measurement.id}")
                return true
            }
        }

        // 如果没有触摸到任何测量，清除选中状态
        if (measurements.any { it.isSelected }) {
            measurements.forEach { it.isSelected = false }
            selectedMeasurement = null
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🔄 Cleared selection")
        }

        return false
    }

    /**
     * 🎯 处理触摸移动事件
     */
    private fun handleTouchMove(touchPoint: PointF): Boolean {
        if (!isDraggingPoint || selectedMeasurement == null) return false

        val measurement = selectedMeasurement!!

        // 检查移动距离是否超过点击容差
        val moveDistance = sqrt((touchPoint.x - lastTouchX).pow(2) + (touchPoint.y - lastTouchY).pow(2))
        if (moveDistance > CLICK_TOLERANCE) {
            longPressStartTime = 0L // 取消长按检测
        }

        // 更新控制点位置
        measurement.updateGeometryFromControlPoints(draggedPointIndex, touchPoint)
        measurement.syncBitmapCoords(imageView)

        measurementUpdateCallback?.invoke()
        return true
    }

    /**
     * 🎯 处理触摸抬起事件
     */
    private fun handleTouchUp(touchPoint: PointF): Boolean {
        val wasDragging = isDraggingPoint

        // 检查是否是长按删除
        if (longPressStartTime > 0 && System.currentTimeMillis() - longPressStartTime >= LONG_PRESS_DURATION) {
            val deleted = deleteSelectedMeasurement()
            if (deleted) {
                Log.d(TAG, "🗑️ Long press delete successful")
            }
        }

        // 重置交互状态
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f

        return wasDragging
    }

    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域）
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 计算UI保护区域 - 与其他Helper保持一致
            val topUIHeight = viewHeight * 0.2f    // 顶部20%为UI区域
            val bottomUIStart = viewHeight * 0.8f  // 底部20%为UI区域

            // 检查是否在UI保护区域
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            return false
        }
    }

    /**
     * 🎯 检查点是否在旋转矩形内部
     */
    private fun isPointInRotatedRectangle(point: PointF, measurement: ThreeRectangleMeasurement): Boolean {
        val rotatedCorners = measurement.calculateRotatedCorners()
        if (rotatedCorners.size < 4) return false

        // 使用射线法检测点是否在多边形内部
        var inside = false
        var j = rotatedCorners.size - 1

        for (i in rotatedCorners.indices) {
            val xi = rotatedCorners[i].x
            val yi = rotatedCorners[i].y
            val xj = rotatedCorners[j].x
            val yj = rotatedCorners[j].y

            if (((yi > point.y) != (yj > point.y)) &&
                (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi)) {
                inside = !inside
            }
            j = i
        }

        return inside
    }

    /**
     * 🔄 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        if (!isInitialized) return

        try {
            measurements.forEach { measurement ->
                measurement.syncViewCoords(imageView)
            }
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🔄 Coordinates synced after scale change")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error syncing coordinates: ${e.message}")
        }
    }

    /**
     * 📊 获取所有测量数据
     */
    fun getAllMeasurements(): List<ThreeRectangleMeasurementData> {
        return measurements.map { measurement ->
            ThreeRectangleMeasurementData(
                id = measurement.id,
                centerPoint = measurement.centerPoint,
                width = measurement.width,
                height = measurement.height,
                rotationAngle = measurement.rotationAngle,
                controlPoints = measurement.viewControlPoints.toList(),
                rotatedCorners = measurement.calculateRotatedCorners(),
                isSelected = measurement.isSelected,
                isEditing = measurement.isEditing,
                isCompleted = measurement.isCompleted,
                textPosition = measurement.textPosition,
                area = measurement.calculateArea(),
                perimeter = measurement.calculatePerimeter(),
                isDragging = isDraggingPoint && selectedMeasurement == measurement,
                draggedPointIndex = if (isDraggingPoint && selectedMeasurement == measurement) draggedPointIndex else -1
            )
        }
    }

    /**
     * 🗑️ 删除选中的测量 - 智能删除逻辑
     */
    fun deleteSelectedMeasurement(): Boolean {
        var selected = selectedMeasurement

        // 如果没有选中的测量，尝试自动选中最后一个测量
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        val removed = measurements.remove(selected)
        if (removed) {
            selectedMeasurement = null
            resetInteractionState()

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            }

            measurementUpdateCallback?.invoke()
            Log.d(TAG, "✅ Three rectangle measurement deleted: ${selected.id}")
            return true
        }
        return false
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f
    }

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean {
        return isDraggingPoint
    }

    /**
     * 🔍 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        return selectedMeasurement != null
    }

    /**
     * 📊 获取测量数量
     */
    fun getMeasurementCount(): Int {
        return measurements.size
    }

    /**
     * 🔍 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.viewControlPoints.any { controlPoint ->
                val distance = sqrt((touchPoint.x - controlPoint.x).pow(2) + (touchPoint.y - controlPoint.y).pow(2))
                distance <= TOUCH_RADIUS
            } || isPointInRotatedRectangle(touchPoint, measurement)
        }
    }

    /**
     * 🔄 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        resetInteractionState()
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔄 Cleared all three rectangle selections")
    }

    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        measurements.clear()
        selectedMeasurement = null
        resetInteractionState()
        measurementUpdateCallback = null
        Log.d(TAG, "🧹 ThreeRectangleMeasureHelper cleaned up")
    }
}
