package com.touptek.measurerealize.utils

import android.graphics.PointF
import org.junit.Test
import org.junit.Assert.*
import kotlin.math.*

/**
 * 🧪 三点矩形测量单元测试
 * 
 * 测试核心功能：
 * - 旋转数学计算
 * - 控制点更新逻辑
 * - 面积和周长计算
 * - 几何约束验证
 */
class ThreeRectangleMeasurementTest {

    private val DELTA = 0.01f // 浮点数比较精度

    @Test
    fun testBasicGeometry() {
        // 创建一个简单的矩形（无旋转）
        val measurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 80f,
            height = 60f,
            rotationAngle = 0f
        )

        // 验证面积和周长计算
        assertEquals(4800f, measurement.calculateArea(), DELTA)
        assertEquals(280f, measurement.calculatePerimeter(), DELTA)
    }

    @Test
    fun testRotationCalculation() {
        // 创建一个45度旋转的矩形
        val measurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 80f,
            height = 60f,
            rotationAngle = Math.PI.toFloat() / 4 // 45度
        )

        // 更新控制点
        measurement.updateControlPointsFromGeometry()

        // 验证控制点数量
        assertEquals(3, measurement.viewControlPoints.size)

        // 验证旋转后的角点数量
        assertEquals(4, measurement.viewRotatedCorners.size)

        // 验证面积和周长在旋转后保持不变
        assertEquals(4800f, measurement.calculateArea(), DELTA)
        assertEquals(280f, measurement.calculatePerimeter(), DELTA)
    }

    @Test
    fun testControlPointConstraints() {
        val measurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 80f,
            height = 60f,
            rotationAngle = 0f
        )

        measurement.updateControlPointsFromGeometry()
        val originalControlPoints = measurement.viewControlPoints.map { PointF(it.x, it.y) }

        // 测试左上角控制点移动（尺寸约束）
        val newTopLeft = PointF(50f, 70f)
        measurement.updateGeometryFromControlPoints(0, newTopLeft)

        // 验证左上角控制点已更新
        assertEquals(newTopLeft.x, measurement.viewControlPoints[0].x, DELTA)
        assertEquals(newTopLeft.y, measurement.viewControlPoints[0].y, DELTA)

        // 验证右下角控制点保持不变（约束条件）
        assertEquals(originalControlPoints[2].x, measurement.viewControlPoints[2].x, DELTA)
        assertEquals(originalControlPoints[2].y, measurement.viewControlPoints[2].y, DELTA)
    }

    @Test
    fun testRotationConstraint() {
        val measurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 80f,
            height = 60f,
            rotationAngle = 0f
        )

        measurement.updateControlPointsFromGeometry()
        val originalCenter = PointF(measurement.centerPoint.x, measurement.centerPoint.y)
        val originalWidth = measurement.width
        val originalHeight = measurement.height

        // 测试右上角控制点移动（旋转约束）
        val newTopRight = PointF(150f, 50f)
        measurement.updateGeometryFromControlPoints(1, newTopRight)

        // 验证中心点保持不变（旋转约束）
        assertEquals(originalCenter.x, measurement.centerPoint.x, DELTA)
        assertEquals(originalCenter.y, measurement.centerPoint.y, DELTA)

        // 验证尺寸保持不变（旋转约束）
        assertEquals(originalWidth, measurement.width, DELTA)
        assertEquals(originalHeight, measurement.height, DELTA)

        // 验证旋转角度已改变
        assertNotEquals(0f, measurement.rotationAngle, DELTA)
    }

    @Test
    fun testSizeConstraint() {
        val measurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 80f,
            height = 60f,
            rotationAngle = Math.PI.toFloat() / 6 // 30度
        )

        measurement.updateControlPointsFromGeometry()
        val originalRotation = measurement.rotationAngle

        // 测试右下角控制点移动（尺寸约束）
        val newBottomRight = PointF(160f, 140f)
        measurement.updateGeometryFromControlPoints(2, newBottomRight)

        // 验证旋转角度保持不变（尺寸约束）
        assertEquals(originalRotation, measurement.rotationAngle, DELTA)

        // 验证尺寸已改变
        assertNotEquals(80f, measurement.width, DELTA)
        assertNotEquals(60f, measurement.height, DELTA)
    }

    @Test
    fun testEdgeCases() {
        // 测试零尺寸矩形
        val zeroSizeMeasurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 0f,
            height = 0f,
            rotationAngle = 0f
        )

        assertEquals(0f, zeroSizeMeasurement.calculateArea(), DELTA)
        assertEquals(0f, zeroSizeMeasurement.calculatePerimeter(), DELTA)

        // 测试极小尺寸矩形
        val tinyMeasurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 1f,
            height = 1f,
            rotationAngle = 0f
        )

        assertEquals(1f, tinyMeasurement.calculateArea(), DELTA)
        assertEquals(4f, tinyMeasurement.calculatePerimeter(), DELTA)
    }

    @Test
    fun testFullRotation() {
        val measurement = ThreeRectangleMeasurement(
            centerPoint = PointF(100f, 100f),
            width = 80f,
            height = 60f,
            rotationAngle = 0f
        )

        val originalArea = measurement.calculateArea()
        val originalPerimeter = measurement.calculatePerimeter()

        // 测试完整旋转（2π）
        measurement.rotationAngle = (2 * Math.PI).toFloat()
        measurement.updateControlPointsFromGeometry()

        // 验证面积和周长在完整旋转后保持不变
        assertEquals(originalArea, measurement.calculateArea(), DELTA)
        assertEquals(originalPerimeter, measurement.calculatePerimeter(), DELTA)
    }
}
