package com.touptek.measurerealize.utils

import android.graphics.*
import android.util.Log
import android.view.MotionEvent
import com.touptek.measurerealize.TpImageView
import java.util.*
import kotlin.math.*

/**
 * 📦 矩形测量实例 - 专业级数据结构
 */
data class RectangleMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [左上, 右上, 右下, 左下] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [左上, 右上, 右下, 左下] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,    // 数值文本位置
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {
    /**
     * 📦 计算矩形面积（使用位图坐标 - 真实面积，不受缩放影响）
     */
    fun calculateArea(): Double {
        if (bitmapPoints.size < 4) return 0.0
        
        val topLeft = bitmapPoints[0]
        val bottomRight = bitmapPoints[2]
        
        val width = abs(bottomRight.x - topLeft.x)
        val height = abs(bottomRight.y - topLeft.y)
        
        return (width * height).toDouble()
    }
    
    /**
     * 📦 计算矩形周长（使用位图坐标 - 真实周长，不受缩放影响）
     */
    fun calculatePerimeter(): Double {
        if (bitmapPoints.size < 4) return 0.0
        
        val topLeft = bitmapPoints[0]
        val bottomRight = bitmapPoints[2]
        
        val width = abs(bottomRight.x - topLeft.x)
        val height = abs(bottomRight.y - topLeft.y)
        
        return 2.0 * (width + height)
    }
    
    /**
     * 🔄 同步位图坐标 - 从视图坐标转换到位图坐标
     */
    fun syncBitmapCoords(imageView: TpImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 🔄 同步视图坐标 - 从位图坐标转换到视图坐标
     */
    fun syncViewCoords(imageView: TpImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
        lastModified = System.currentTimeMillis()
    }
    
    /**
     * 📍 检查点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, pointIndex: Int, touchRadius: Float): Boolean {
        if (pointIndex !in 0 until viewPoints.size) return false
        
        val point = viewPoints[pointIndex]
        val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
        return distance <= touchRadius
    }
    
    /**
     * 📍 获取最近的控制点索引（只考虑左上角和右下角）
     */
    fun getNearestControlPointIndex(touchPoint: PointF): Int {
        if (viewPoints.size < 4) return -1
        
        val topLeft = viewPoints[0]
        val bottomRight = viewPoints[2]
        
        val distanceToTopLeft = sqrt((touchPoint.x - topLeft.x).pow(2) + (touchPoint.y - topLeft.y).pow(2))
        val distanceToBottomRight = sqrt((touchPoint.x - bottomRight.x).pow(2) + (touchPoint.y - bottomRight.y).pow(2))
        
        return if (distanceToTopLeft <= distanceToBottomRight) 0 else 2
    }
    
    /**
     * 📦 更新矩形控制点 - 矩形约束逻辑
     */
    fun updateWithRectangleConstraint(pointIndex: Int, newPoint: PointF) {
        if (viewPoints.size < 4) return
        
        when (pointIndex) {
            0 -> { // 左上角控制点移动
                viewPoints[0] = newPoint  // 左上角
                viewPoints[1] = PointF(viewPoints[2].x, newPoint.y)  // 右上角
                viewPoints[3] = PointF(newPoint.x, viewPoints[2].y)  // 左下角
                // 右下角保持不变
            }
            2 -> { // 右下角控制点移动
                viewPoints[2] = newPoint  // 右下角
                viewPoints[1] = PointF(newPoint.x, viewPoints[0].y)  // 右上角
                viewPoints[3] = PointF(viewPoints[0].x, newPoint.y)  // 左下角
                // 左上角保持不变
            }
        }
        
        updateTextPosition()
    }
    
    /**
     * 📝 更新文本位置 - 矩形中心
     */
    fun updateTextPosition() {
        if (viewPoints.size >= 4) {
            val centerX = (viewPoints[0].x + viewPoints[2].x) / 2
            val centerY = (viewPoints[0].y + viewPoints[2].y) / 2
            textPosition = PointF(centerX, centerY)
        }
    }
    
    /**
     * 🔄 坐标转换方法 - 视图坐标到位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }
    
    /**
     * 🔄 坐标转换方法 - 位图坐标到视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: TpImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }
}

/**
 * 📦 专业级矩形测量助手 - 遵循现有架构模式
 */
class RectangleMeasureHelper {
    companion object {
        private const val TAG = "RectangleMeasureHelper"
        
        // 交互参数 - 与其他测量功能保持一致
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间
        private const val CLICK_TOLERANCE = 30f        // 点击容差
        
        // 默认尺寸参数
        private const val DEFAULT_RECT_SIZE = 200f     // 默认矩形尺寸
    }
    
    // 核心组件
    private lateinit var imageView: TpImageView
    private lateinit var originalBitmap: Bitmap
    private var isInitialized = false
    
    // 测量数据
    private val measurements = mutableListOf<RectangleMeasurement>()
    private var selectedMeasurement: RectangleMeasurement? = null
    
    // 交互状态管理 - 与其他Helper保持一致
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    
    // 回调函数
    private var measurementUpdateCallback: (() -> Unit)? = null
    
    /**
     * 🚀 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.originalBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        this.isInitialized = true
        
        Log.d(TAG, "🚀 RectangleMeasureHelper initialized")
    }
    
    /**
     * 🔄 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }
    
    /**
     * 📦 开始新的矩形测量 - 在屏幕中心生成默认矩形
     */
    fun startNewMeasurement(): String {
        if (!isInitialized) {
            Log.w(TAG, "Helper not initialized")
            return ""
        }
        
        try {
            Log.d(TAG, "📦 Creating new rectangle measurement")
            
            // 计算屏幕中心位置
            val centerX = imageView.width / 2f
            val centerY = imageView.height / 2f
            val halfSize = DEFAULT_RECT_SIZE / 2f
            
            // 创建矩形测量实例
            val measurement = RectangleMeasurement().apply {
                // 设置视图坐标：[左上, 右上, 右下, 左下]
                viewPoints.add(PointF(centerX - halfSize, centerY - halfSize)) // 左上角
                viewPoints.add(PointF(centerX + halfSize, centerY - halfSize)) // 右上角
                viewPoints.add(PointF(centerX + halfSize, centerY + halfSize)) // 右下角
                viewPoints.add(PointF(centerX - halfSize, centerY + halfSize)) // 左下角
                
                isCompleted = true
                isSelected = true // 新创建的测量默认选中
                
                updateTextPosition()
            }
            
            // 同步位图坐标
            measurement.syncBitmapCoords(imageView)
            
            // 取消其他测量的选中状态
            measurements.forEach {
                it.isSelected = false
                it.isEditing = false
            }
            
            // 添加到测量列表
            measurements.add(measurement)
            selectedMeasurement = measurement
            
            Log.d(TAG, "✅ New rectangle measurement created: ${measurement.id}")
            Log.d(TAG, "📊 Current measurements count: ${measurements.size}")
            
            // 触发更新回调
            measurementUpdateCallback?.invoke()
            
            return measurement.id
            
        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to create new rectangle measurement: ${e.message}")
            e.printStackTrace()
            return ""
        }
    }
    
    /**
     * 📦 添加新的矩形测量（在测量模式下）
     */
    fun addNewMeasurement(): String {
        return startNewMeasurement()
    }

    /**
     * 🎯 处理触摸事件 - 标准触摸处理模式
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                return handleTouchDown(touchPoint, viewWidth, viewHeight)
            }
            MotionEvent.ACTION_MOVE -> {
                return handleTouchMove(touchPoint)
            }
            MotionEvent.ACTION_UP -> {
                return handleTouchUp(touchPoint, viewWidth, viewHeight)
            }
        }

        return false
    }

    /**
     * 🎯 处理触摸按下事件
     */
    private fun handleTouchDown(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        // 记录触摸起始信息
        longPressStartTime = System.currentTimeMillis()
        lastTouchX = touchPoint.x
        lastTouchY = touchPoint.y

        // 检查控制点触摸（优先级最高）- 只检查左上角和右下角
        for (measurement in measurements.reversed()) {
            // 检查左上角控制点
            if (measurement.isPointInTouchRange(touchPoint, 0, TOUCH_RADIUS)) {
                selectedMeasurement = measurement
                draggedPointIndex = 0
                isDraggingPoint = true

                // 设置选中状态
                measurements.forEach { it.isSelected = false }
                measurement.isSelected = true

                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🎯 Started dragging top-left control point")
                return true
            }

            // 检查右下角控制点
            if (measurement.isPointInTouchRange(touchPoint, 2, TOUCH_RADIUS)) {
                selectedMeasurement = measurement
                draggedPointIndex = 2
                isDraggingPoint = true

                // 设置选中状态
                measurements.forEach { it.isSelected = false }
                measurement.isSelected = true

                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🎯 Started dragging bottom-right control point")
                return true
            }
        }

        // 检查矩形内部点击（用于选择整个矩形）
        for (measurement in measurements.reversed()) {
            if (isPointInRectangle(touchPoint, measurement)) {
                measurements.forEach { it.isSelected = false }
                measurement.isSelected = true
                selectedMeasurement = measurement
                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🎯 Selected rectangle by clicking inside: ${measurement.id}")
                return true
            }
        }

        // 🔧 智能空白区域检测 - 避免UI按钮区域被误判
        if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
            measurements.forEach { it.isSelected = false }
            selectedMeasurement = null
            Log.d(TAG, "🔄 Cleared all selections - empty area clicked in image content")
            measurementUpdateCallback?.invoke()
        } else {
            Log.d(TAG, "🛡️ Clicked in UI area, preserving selection state")
        }

        return false // 让ImageView处理缩放
    }

    /**
     * 🎯 处理触摸移动事件
     */
    private fun handleTouchMove(touchPoint: PointF): Boolean {
        if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
            // 正常拖拽处理
            selectedMeasurement!!.updateWithRectangleConstraint(draggedPointIndex, touchPoint)
            selectedMeasurement!!.syncBitmapCoords(imageView)
            measurementUpdateCallback?.invoke()
            return true
        } else if (!isDraggingPoint && selectedMeasurement != null) {
            // 触摸恢复机制
            val measurement = selectedMeasurement!!
            if (measurement.isPointInTouchRange(touchPoint, 0, TOUCH_RADIUS)) {
                draggedPointIndex = 0
                isDraggingPoint = true
                // 立即处理这次移动
                measurement.updateWithRectangleConstraint(draggedPointIndex, touchPoint)
                measurement.syncBitmapCoords(imageView)
                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🔄 Touch recovery successful - resumed dragging top-left point")
                return true
            } else if (measurement.isPointInTouchRange(touchPoint, 2, TOUCH_RADIUS)) {
                draggedPointIndex = 2
                isDraggingPoint = true
                // 立即处理这次移动
                measurement.updateWithRectangleConstraint(draggedPointIndex, touchPoint)
                measurement.syncBitmapCoords(imageView)
                measurementUpdateCallback?.invoke()
                Log.d(TAG, "🔄 Touch recovery successful - resumed dragging bottom-right point")
                return true
            }
        }
        return false
    }

    /**
     * 🎯 处理触摸抬起事件
     */
    private fun handleTouchUp(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        val touchDuration = System.currentTimeMillis() - longPressStartTime
        val touchDistance = sqrt((touchPoint.x - lastTouchX) * (touchPoint.x - lastTouchX) +
                                (touchPoint.y - lastTouchY) * (touchPoint.y - lastTouchY))
        val wasDragging = isDraggingPoint

        var handled = false

        // 重置拖拽状态
        isDraggingPoint = false
        draggedPointIndex = -1

        if (wasDragging) {
            // 拖拽完成
            measurementUpdateCallback?.invoke()
            handled = true
        }

        // 长按删除
        if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, 0, TOUCH_RADIUS) ||
                    measurement.isPointInTouchRange(touchPoint, 2, TOUCH_RADIUS) ||
                    isPointInRectangle(touchPoint, measurement)) {
                    measurements.remove(measurement)
                    if (selectedMeasurement == measurement) {
                        selectedMeasurement = null
                    }
                    measurementUpdateCallback?.invoke()
                    Log.d(TAG, "🗑️ Long press deleted rectangle: ${measurement.id}")
                    handled = true
                    break
                }
            }
        }

        // 轻触选中
        if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
            for (measurement in measurements.reversed()) {
                if (measurement.isPointInTouchRange(touchPoint, 0, TOUCH_RADIUS) ||
                    measurement.isPointInTouchRange(touchPoint, 2, TOUCH_RADIUS) ||
                    isPointInRectangle(touchPoint, measurement)) {
                    measurements.forEach { it.isSelected = false }
                    measurement.isSelected = true
                    selectedMeasurement = measurement
                    measurementUpdateCallback?.invoke()
                    Log.d(TAG, "🎯 Light tap selected rectangle: ${measurement.id}")
                    return true
                }
            }
        }

        return handled
    }

    /**
     * 📍 检查点是否在矩形内部
     */
    private fun isPointInRectangle(touchPoint: PointF, measurement: RectangleMeasurement): Boolean {
        if (measurement.viewPoints.size < 4) return false

        val topLeft = measurement.viewPoints[0]
        val bottomRight = measurement.viewPoints[2]

        return touchPoint.x >= topLeft.x && touchPoint.x <= bottomRight.x &&
               touchPoint.y >= topLeft.y && touchPoint.y <= bottomRight.y
    }

    /**
     * 🔧 智能UI区域保护 - 与LineMeasureHelper保持一致
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 🔄 缩放变化时同步坐标
     */
    fun onScaleChanged() {
        if (!isInitialized) return

        try {
            measurements.forEach { measurement ->
                measurement.syncViewCoords(imageView)
                measurement.updateTextPosition()
            }
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🔄 Coordinates synced after scale change")
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error syncing coordinates: ${e.message}")
        }
    }

    /**
     * 📊 获取所有测量数据
     */
    fun getAllMeasurements(): List<RectangleMeasurementData> {
        return measurements.map { measurement ->
            RectangleMeasurementData(
                id = measurement.id,
                viewPoints = measurement.viewPoints.toList(),
                bitmapPoints = measurement.bitmapPoints.toList(),
                isSelected = measurement.isSelected,
                isEditing = measurement.isEditing,
                isCompleted = measurement.isCompleted,
                textPosition = measurement.textPosition,
                area = measurement.calculateArea(),
                perimeter = measurement.calculatePerimeter(),
                isDragging = isDraggingPoint && selectedMeasurement == measurement,
                draggedPointIndex = if (isDraggingPoint && selectedMeasurement == measurement) draggedPointIndex else -1
            )
        }
    }

    /**
     * 🗑️ 删除选中的测量 - 智能删除逻辑
     */
    fun deleteSelectedMeasurement(): Boolean {
        var selected = selectedMeasurement

        // 🔄 如果没有选中的测量，尝试自动选中最后一个测量
        if (selected == null && measurements.isNotEmpty()) {
            val lastMeasurement = measurements.last()
            lastMeasurement.isSelected = true
            selectedMeasurement = lastMeasurement
            selected = lastMeasurement
            Log.d(TAG, "🔄 Auto-selected last measurement for deletion: ${lastMeasurement.id}")
        }

        if (selected == null) {
            Log.w(TAG, "⚠️ No measurements available for deletion")
            return false
        }

        val removed = measurements.remove(selected)
        if (removed) {
            selectedMeasurement = null
            resetInteractionState()

            // 如果还有其他测量，选中最后一个
            if (measurements.isNotEmpty()) {
                val lastMeasurement = measurements.last()
                lastMeasurement.isSelected = true
                selectedMeasurement = lastMeasurement
                Log.d(TAG, "🎯 Auto-selected last measurement: ${lastMeasurement.id}")
            }

            measurementUpdateCallback?.invoke()
            Log.d(TAG, "✅ Rectangle measurement deleted: ${selected.id}")
            return true
        }
        return false
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f
    }

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean {
        return isDraggingPoint
    }

    /**
     * 🔍 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        return selectedMeasurement != null
    }

    /**
     * 📊 获取测量数量
     */
    fun getMeasurementCount(): Int {
        return measurements.size
    }

    /**
     * 🔍 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, 0, TOUCH_RADIUS) ||
            measurement.isPointInTouchRange(touchPoint, 2, TOUCH_RADIUS) ||
            isPointInRectangle(touchPoint, measurement)
        }
    }

    /**
     * 🔄 清除所有选中状态
     */
    fun clearSelection() {
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        resetInteractionState()
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔄 Cleared all rectangle selections")
    }

    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        measurements.clear()
        selectedMeasurement = null
        resetInteractionState()
        measurementUpdateCallback = null
        Log.d(TAG, "🧹 RectangleMeasureHelper cleaned up")
    }
}
