package com.touptek.xcamview.activity.browse.imagemanagement;

import java.lang.System;

@kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u000b\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b(\u0018\u0000 V2\u00020\u0001:\u0003UVWB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u001d\u001a\u00020\u001eH\u0002J\b\u0010\u001f\u001a\u00020\u001eH\u0002J\u0006\u0010 \u001a\u00020\u001eJ\b\u0010!\u001a\u00020\u001eH\u0002J\b\u0010\"\u001a\u00020\u001eH\u0002J\b\u0010#\u001a\u00020\u001eH\u0002J\b\u0010$\u001a\u00020\u001eH\u0002J\b\u0010%\u001a\u00020\u001eH\u0002J\b\u0010&\u001a\u00020\u000fH\u0002J\b\u0010\'\u001a\u00020\u001eH\u0002J\u0012\u0010(\u001a\u00020\u001e2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J$\u0010+\u001a\u00020,2\u0006\u0010-\u001a\u00020.2\b\u0010/\u001a\u0004\u0018\u0001002\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\b\u00101\u001a\u00020\u001eH\u0016J\b\u00102\u001a\u00020\u001eH\u0016J\b\u00103\u001a\u00020\u001eH\u0016J\u001a\u00104\u001a\u00020\u001e2\u0006\u00105\u001a\u00020,2\b\u0010)\u001a\u0004\u0018\u00010*H\u0016J\b\u00106\u001a\u00020\u001eH\u0002J\b\u00107\u001a\u00020\u001eH\u0002J\b\u00108\u001a\u00020\u001eH\u0002J\b\u00109\u001a\u00020\u001eH\u0002J\b\u0010:\u001a\u00020\u001eH\u0002J\b\u0010;\u001a\u00020\u001eH\u0002J\b\u0010<\u001a\u00020\u001eH\u0002J\b\u0010=\u001a\u00020\u001eH\u0002J\u0006\u0010>\u001a\u00020\u001eJ\u0006\u0010?\u001a\u00020\u001eJ\u0006\u0010@\u001a\u00020\u001eJ\b\u0010A\u001a\u00020\u001eH\u0002J\b\u0010B\u001a\u00020\u001eH\u0002J\b\u0010C\u001a\u00020\u001eH\u0002J\u0006\u0010D\u001a\u00020\u001eJ\b\u0010E\u001a\u00020\u001eH\u0002J\b\u0010F\u001a\u00020\u001eH\u0002J\b\u0010G\u001a\u00020\u001eH\u0002J\b\u0010H\u001a\u00020\u001eH\u0002J\u0006\u0010I\u001a\u00020\u001eJ\u0006\u0010J\u001a\u00020\u001eJ\u0006\u0010K\u001a\u00020\u001eJ\u0006\u0010L\u001a\u00020\u001eJ\u0006\u0010M\u001a\u00020\u001eJ\u0006\u0010N\u001a\u00020\u001eJ\b\u0010O\u001a\u00020\u001eH\u0002J\b\u0010P\u001a\u00020\u001eH\u0002J\b\u0010Q\u001a\u00020\u001eH\u0002J\u0006\u0010R\u001a\u00020\u001eJ\b\u0010S\u001a\u00020\u001eH\u0002J\b\u0010T\u001a\u00020\u001eH\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0006X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0010\u0010\u0007\u001a\u0004\u0018\u00010\bX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\nX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\r0\fX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0011\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0012\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0013\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0014\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0015\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0016\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0017\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0018\u001a\u00020\u000fX\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0019\u001a\u00020\u001aX\u0082.\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u001b\u001a\u00020\u001cX\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006X"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment;", "Landroidx/fragment/app/DialogFragment;", "()V", "binding", "Lcom/touptek/xcamview/databinding/ImageViewerBinding;", "bottomButtonState", "Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$BottomButtonState;", "currentBitmap", "Landroid/graphics/Bitmap;", "currentPosition", "", "imagePaths", "", "", "isMeasuringAngle", "", "isMeasuringFourPointAngle", "isMeasuringHorizonLine", "isMeasuringLine", "isMeasuringParallelLines", "isMeasuringPoint", "isMeasuringRectangle", "isMeasuringThreeRectangle", "isMeasuringThreeVertical", "isMeasuringVerticalLine", "measurementManager", "Lcom/touptek/measurerealize/MeasurementManager;", "topToolbarState", "Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$TopToolbarState;", "cycleBottomButtonsState", "", "cycleTopToolbarState", "deleteSelectedMeasurement", "enableSingleTapListener", "forceToolbarState", "hideBottomButtons", "hideMeasurementToolbar", "initializeMeasurement", "isDraggingMeasurementPoint", "loadCurrentImage", "onCreate", "savedInstanceState", "Landroid/os/Bundle;", "onCreateView", "Landroid/view/View;", "inflater", "Landroid/view/LayoutInflater;", "container", "Landroid/view/ViewGroup;", "onDestroy", "onDestroyView", "onStart", "onViewCreated", "view", "protectMeasurementMode", "setupClickListeners", "setupImageViewClickHandler", "setupMeasurementToolbarButtons", "showBottomButtons", "showMeasurementToolbar", "showNextImage", "showPreviousImage", "startAngleMeasurementMixed", "startFourPointAngleMeasurement", "startFourPointAngleMeasurementMixed", "startHorizonLineMeasurement", "startLineMeasurement", "startParallelLinesMeasurement", "startPointMeasurement", "startRectangleMeasurement", "startThreeRectangleMeasurement", "startThreeVerticalMeasurement", "startVerticalLineMeasurement", "stopAngleMeasurement", "stopFourPointAngleMeasurement", "stopHorizonLineMeasurement", "stopLineMeasurement", "stopParallelLinesMeasurement", "stopPointMeasurement", "stopRectangleMeasurement", "stopThreeRectangleMeasurement", "stopThreeVerticalMeasurement", "stopVerticalLineMeasurement", "updateButtonStates", "updateCurrentBitmap", "BottomButtonState", "Companion", "TopToolbarState", "app_debug"})
public final class TpImageDecodeDialogFragment extends androidx.fragment.app.DialogFragment {
    private com.touptek.xcamview.databinding.ImageViewerBinding binding;
    private java.util.List<java.lang.String> imagePaths;
    private int currentPosition = 0;
    private com.touptek.measurerealize.MeasurementManager measurementManager;
    private boolean isMeasuringAngle = false;
    private boolean isMeasuringFourPointAngle = false;
    private boolean isMeasuringPoint = false;
    private boolean isMeasuringLine = false;
    private boolean isMeasuringHorizonLine = false;
    private boolean isMeasuringVerticalLine = false;
    private boolean isMeasuringParallelLines = false;
    private boolean isMeasuringThreeVertical = false;
    private boolean isMeasuringRectangle = false;
    private boolean isMeasuringThreeRectangle = false;
    private android.graphics.Bitmap currentBitmap;
    private com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.TopToolbarState topToolbarState = com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.TopToolbarState.VISIBLE;
    private com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.BottomButtonState bottomButtonState = com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.BottomButtonState.VISIBLE;
    @org.jetbrains.annotations.NotNull
    public static final com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment.Companion Companion = null;
    private static final java.lang.String ARG_IMAGE_PATHS = "image_paths";
    private static final java.lang.String ARG_CURRENT_POSITION = "current_position";
    
    public TpImageDecodeDialogFragment() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    @java.lang.Override
    public android.view.View onCreateView(@org.jetbrains.annotations.NotNull
    android.view.LayoutInflater inflater, @org.jetbrains.annotations.Nullable
    android.view.ViewGroup container, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
        return null;
    }
    
    @java.lang.Override
    public void onViewCreated(@org.jetbrains.annotations.NotNull
    android.view.View view, @org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onCreate(@org.jetbrains.annotations.Nullable
    android.os.Bundle savedInstanceState) {
    }
    
    @java.lang.Override
    public void onStart() {
    }
    
    private final void setupClickListeners() {
    }
    
    private final void setupImageViewClickHandler() {
    }
    
    /**
     * 🎯 设置测量工具栏按钮点击事件 - 完整的23个按钮
     */
    private final void setupMeasurementToolbarButtons() {
    }
    
    private final void loadCurrentImage() {
    }
    
    private final void showPreviousImage() {
    }
    
    private final void showNextImage() {
    }
    
    private final void updateButtonStates() {
    }
    
    private final void cycleBottomButtonsState() {
    }
    
    private final void cycleTopToolbarState() {
    }
    
    private final void showBottomButtons() {
    }
    
    private final void hideBottomButtons() {
    }
    
    private final void showMeasurementToolbar() {
    }
    
    private final void hideMeasurementToolbar() {
    }
    
    /**
     * � 强制工具栏状态 - 确保测量工具栏显示并锁定
     */
    private final void forceToolbarState() {
    }
    
    /**
     * ✅ 启用单击监听器 - 恢复正常UI交互
     */
    private final void enableSingleTapListener() {
    }
    
    /**
     * �🛡️ 保护测量模式 - 添加测量模式专用保护机制
     */
    private final void protectMeasurementMode() {
    }
    
    /**
     * � 检查是否正在拖拽测量点
     */
    private final boolean isDraggingMeasurementPoint() {
        return false;
    }
    
    /**
     * ��🚀 初始化测量功能 - 使用封装的MeasurementManager
     */
    private final void initializeMeasurement() {
    }
    
    /**
     * 🎯 开始角度测量 - 混合模式支持
     */
    public final void startAngleMeasurementMixed() {
    }
    
    /**
     * 🎯 开始四点角度测量 - 与三点角度测量保持一致的架构
     */
    public final void startFourPointAngleMeasurement() {
    }
    
    /**
     * 🎯 开始四点角度测量 - 混合模式支持
     */
    public final void startFourPointAngleMeasurementMixed() {
    }
    
    /**
     * 🎯 开始点测量 - 与角度测量保持一致的架构
     */
    public final void startPointMeasurement() {
    }
    
    /**
     * ⏹️ 停止点测量
     */
    public final void stopPointMeasurement() {
    }
    
    /**
     * 📏 开始线段测量 - 与其他测量保持一致的架构
     */
    private final void startLineMeasurement() {
    }
    
    /**
     * 🛑 停止线段测量
     */
    public final void stopLineMeasurement() {
    }
    
    /**
     * 📏 开始水平线测量 - 与其他测量保持一致的架构
     */
    private final void startHorizonLineMeasurement() {
    }
    
    /**
     * 🛑 停止水平线测量
     */
    public final void stopHorizonLineMeasurement() {
    }
    
    /**
     * 📏 开始垂直线测量 - 与其他测量保持一致的架构
     */
    private final void startVerticalLineMeasurement() {
    }
    
    /**
     * 🛑 停止垂直线测量
     */
    public final void stopVerticalLineMeasurement() {
    }
    
    /**
     * 📏 开始平行线测量 - 与其他测量保持一致的架构
     */
    private final void startParallelLinesMeasurement() {
    }
    
    /**
     * 🛑 停止平行线测量
     */
    public final void stopParallelLinesMeasurement() {
    }
    
    /**
     * 📏 开始三垂直测量 - 极简实现
     */
    private final void startThreeVerticalMeasurement() {
    }
    
    /**
     * 🛑 停止三垂直测量
     */
    private final void stopThreeVerticalMeasurement() {
    }
    
    /**
     * 📦 开始矩形测量 - 极简实现
     */
    private final void startRectangleMeasurement() {
    }
    
    /**
     * 🛑 停止矩形测量
     */
    private final void stopRectangleMeasurement() {
    }
    
    /**
     * 🎯 开始三点矩形测量 - 支持旋转的矩形
     */
    private final void startThreeRectangleMeasurement() {
    }
    
    /**
     * 🛑 停止三点矩形测量
     */
    private final void stopThreeRectangleMeasurement() {
    }
    
    /**
     * ⏹️ 停止四点角度测量
     */
    public final void stopFourPointAngleMeasurement() {
    }
    
    /**
     * ⏹️ 停止角度测量 - 极简API调用，解锁UI状态
     */
    public final void stopAngleMeasurement() {
    }
    
    /**
     * 🗑️ 删除选中的测量
     */
    public final void deleteSelectedMeasurement() {
    }
    
    /**
     * 🧹 清理测量资源
     */
    @java.lang.Override
    public void onDestroyView() {
    }
    
    /**
     * 🖼️ 加载图片时更新位图引用
     */
    private final void updateCurrentBitmap() {
    }
    
    @java.lang.Override
    public void onDestroy() {
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0082\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$TopToolbarState;", "", "(Ljava/lang/String;I)V", "VISIBLE", "HIDDEN", "app_debug"})
    static enum TopToolbarState {
        /*public static final*/ VISIBLE /* = new VISIBLE() */,
        /*public static final*/ HIDDEN /* = new HIDDEN() */;
        
        TopToolbarState() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0004\b\u0082\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004\u00a8\u0006\u0005"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$BottomButtonState;", "", "(Ljava/lang/String;I)V", "VISIBLE", "HIDDEN", "app_debug"})
    static enum BottomButtonState {
        /*public static final*/ VISIBLE /* = new VISIBLE() */,
        /*public static final*/ HIDDEN /* = new HIDDEN() */;
        
        BottomButtonState() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 7, 1}, k = 1, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0006\u001a\u00020\u00072\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t2\u0006\u0010\n\u001a\u00020\u000bR\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\f"}, d2 = {"Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment$Companion;", "", "()V", "ARG_CURRENT_POSITION", "", "ARG_IMAGE_PATHS", "newInstance", "Lcom/touptek/xcamview/activity/browse/imagemanagement/TpImageDecodeDialogFragment;", "imagePaths", "", "currentPosition", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.touptek.xcamview.activity.browse.imagemanagement.TpImageDecodeDialogFragment newInstance(@org.jetbrains.annotations.NotNull
        java.util.List<java.lang.String> imagePaths, int currentPosition) {
            return null;
        }
    }
}