package com.touptek.measurerealize.utils

import android.graphics.PointF
import android.util.Log
import android.widget.ImageView
import android.view.MotionEvent
import android.graphics.Bitmap
import android.graphics.Matrix
import com.touptek.measurerealize.TpImageView
import java.util.UUID
import kotlin.math.*
import kotlin.system.measureNanoTime

/**
 * 📏 三垂直测量实例 - 专业级数据结构
 */
data class ThreeVerticalMeasurement(
    val id: String = UUID.randomUUID().toString(),
    var viewPoints: MutableList<PointF> = mutableListOf(),  // [基准线起点, 基准线终点, 独立点] 视图坐标
    var bitmapPoints: MutableList<PointF> = mutableListOf(), // [基准线起点, 基准线终点, 独立点] 位图坐标
    var isSelected: Boolean = false,
    var isEditing: Boolean = false,
    var isCompleted: Boolean = false,
    var textPosition: PointF? = null,    // 距离文本位置
    var perpendicularFoot: PointF? = null, // 垂足位置（实时计算）
    var creationTime: Long = System.currentTimeMillis(),
    var lastModified: Long = System.currentTimeMillis()
) {
    /**
     * 📏 计算垂直距离（使用位图坐标 - 真实距离，不受缩放影响）
     */
    fun calculatePerpendicularDistance(): Double {
        if (bitmapPoints.size < 3) return 0.0
        
        val lineStart = bitmapPoints[0]
        val lineEnd = bitmapPoints[1]
        val point = bitmapPoints[2]
        
        return calculatePointToLineDistance(point, lineStart, lineEnd)
    }

    /**
     * 🔧 计算点到直线的距离
     */
    private fun calculatePointToLineDistance(point: PointF, lineStart: PointF, lineEnd: PointF): Double {
        val A = lineStart
        val B = lineEnd
        val P = point
        
        // 如果线段长度为0，返回点到点的距离
        val lineLength = sqrt((B.x - A.x).pow(2) + (B.y - A.y).pow(2))
        if (lineLength == 0f) {
            return sqrt((P.x - A.x).pow(2) + (P.y - A.y).pow(2)).toDouble()
        }
        
        // 使用点到直线距离公式：|ax + by + c| / sqrt(a² + b²)
        val a = B.y - A.y
        val b = A.x - B.x
        val c = B.x * A.y - A.x * B.y
        
        return abs(a * P.x + b * P.y + c) / sqrt(a * a + b * b).toDouble()
    }

    /**
     * 🔧 计算垂足位置
     */
    fun calculatePerpendicularFoot(imageView: ImageView): PointF? {
        if (viewPoints.size < 3) return null
        
        val lineStart = viewPoints[0]
        val lineEnd = viewPoints[1]
        val point = viewPoints[2]
        
        return calculatePerpendicularFootPoint(point, lineStart, lineEnd)
    }

    /**
     * 🔧 计算垂足位置的数学实现
     */
    private fun calculatePerpendicularFootPoint(point: PointF, lineStart: PointF, lineEnd: PointF): PointF {
        val A = lineStart
        val B = lineEnd  
        val P = point
        
        val AP = PointF(P.x - A.x, P.y - A.y)
        val AB = PointF(B.x - A.x, B.y - A.y)
        
        val AB_squared = AB.x * AB.x + AB.y * AB.y
        if (AB_squared == 0f) return A // 线段长度为0
        
        val t = (AP.x * AB.x + AP.y * AB.y) / AB_squared
        // 不限制t的范围，允许垂足在线段延长线上
        
        return PointF(A.x + t * AB.x, A.y + t * AB.y)
    }

    /**
     * 🎯 更新约束逻辑 - 当任意点移动时更新相关几何属性
     */
    fun updateWithConstraints(pointIndex: Int, newPoint: PointF, imageView: ImageView) {
        when (pointIndex) {
            0, 1 -> { // 基准线端点移动
                if (viewPoints.size >= 3) {
                    viewPoints[pointIndex] = newPoint
                    // 重新计算垂足位置
                    perpendicularFoot = calculatePerpendicularFoot(imageView)
                    updateTextPosition()
                    markAsModified()
                }
            }
            2 -> { // 独立点移动
                if (viewPoints.size >= 3) {
                    viewPoints[2] = newPoint
                    // 重新计算垂足位置
                    perpendicularFoot = calculatePerpendicularFoot(imageView)
                    updateTextPosition()
                    markAsModified()
                }
            }
        }
    }

    /**
     * 🔄 同步位图坐标
     */
    fun syncBitmapCoords(imageView: ImageView) {
        bitmapPoints.clear()
        viewPoints.forEach { viewPoint ->
            val bitmapPoint = convertViewToBitmapCoords(viewPoint, imageView)
            bitmapPoints.add(bitmapPoint)
        }
    }

    /**
     * 🔄 同步视图坐标（缩放变化时调用）
     */
    fun syncViewCoords(imageView: ImageView) {
        viewPoints.clear()
        bitmapPoints.forEach { bitmapPoint ->
            val viewPoint = convertBitmapToViewCoords(bitmapPoint, imageView)
            viewPoints.add(viewPoint)
        }
        // 重新计算垂足位置
        perpendicularFoot = calculatePerpendicularFoot(imageView)
        updateTextPosition()
    }

    /**
     * 🔄 坐标转换：视图坐标 → 位图坐标
     */
    private fun convertViewToBitmapCoords(viewPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val inverseMatrix = Matrix()
        return if (matrix.invert(inverseMatrix)) {
            val point = FloatArray(2) { 0f }
            point[0] = viewPoint.x
            point[1] = viewPoint.y
            inverseMatrix.mapPoints(point)
            PointF(point[0], point[1])
        } else {
            PointF(viewPoint.x, viewPoint.y)
        }
    }

    /**
     * 🔄 坐标转换：位图坐标 → 视图坐标
     */
    private fun convertBitmapToViewCoords(bitmapPoint: PointF, imageView: ImageView): PointF {
        val matrix = imageView.imageMatrix
        val point = FloatArray(2) { 0f }
        point[0] = bitmapPoint.x
        point[1] = bitmapPoint.y
        matrix.mapPoints(point)
        return PointF(point[0], point[1])
    }

    /**
     * 📝 更新文本位置
     */
    fun updateTextPosition() {
        if (viewPoints.size >= 3 && perpendicularFoot != null) {
            val point = viewPoints[2]
            val foot = perpendicularFoot!!
            // 文本位置在垂线段的中点偏移处
            textPosition = PointF(
                (point.x + foot.x) / 2 + 20f,
                (point.y + foot.y) / 2 - 20f
            )
        }
    }

    /**
     * 🔄 标记为已修改
     */
    private fun markAsModified() {
        lastModified = System.currentTimeMillis()
    }

    /**
     * 🎯 检查点是否在触摸范围内
     */
    fun isPointInTouchRange(touchPoint: PointF, touchRadius: Float): Boolean {
        return viewPoints.any { point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            distance <= touchRadius
        }
    }

    /**
     * 🎯 获取最近的点索引
     */
    fun getNearestPointIndex(touchPoint: PointF): Int {
        var nearestIndex = -1
        var minDistance = Float.MAX_VALUE
        
        viewPoints.forEachIndexed { index, point ->
            val distance = sqrt((touchPoint.x - point.x).pow(2) + (touchPoint.y - point.y).pow(2))
            if (distance < minDistance) {
                minDistance = distance
                nearestIndex = index
            }
        }
        
        return nearestIndex
    }

    /**
     * 🎯 检查触摸点是否在基准线段上
     */
    fun isPointOnBaseLine(touchPoint: PointF, touchRadius: Float): Boolean {
        if (viewPoints.size < 2) return false
        
        val lineStart = viewPoints[0]
        val lineEnd = viewPoints[1]
        
        // 计算点到线段的距离
        val distance = calculatePointToLineSegmentDistance(touchPoint, lineStart, lineEnd)
        return distance <= touchRadius
    }

    /**
     * 🔧 计算点到线段的距离（不是直线）
     */
    private fun calculatePointToLineSegmentDistance(point: PointF, lineStart: PointF, lineEnd: PointF): Float {
        val A = lineStart
        val B = lineEnd
        val P = point
        
        val AP = PointF(P.x - A.x, P.y - A.y)
        val AB = PointF(B.x - A.x, B.y - A.y)
        
        val AB_squared = AB.x * AB.x + AB.y * AB.y
        if (AB_squared == 0f) {
            // 线段长度为0，返回点到点的距离
            return sqrt(AP.x * AP.x + AP.y * AP.y)
        }
        
        val t = (AP.x * AB.x + AP.y * AB.y) / AB_squared
        
        return when {
            t < 0 -> sqrt(AP.x * AP.x + AP.y * AP.y) // 最近点是A
            t > 1 -> {
                val BP = PointF(P.x - B.x, P.y - B.y)
                sqrt(BP.x * BP.x + BP.y * BP.y) // 最近点是B
            }
            else -> {
                val projection = PointF(A.x + t * AB.x, A.y + t * AB.y)
                val PP = PointF(P.x - projection.x, P.y - projection.y)
                sqrt(PP.x * PP.x + PP.y * PP.y) // 最近点在线段上
            }
        }
    }
}

/**
 * 📏 三垂直测量助手类 - 专业级实现
 */
class ThreeVerticalMeasureHelper {
    companion object {
        private const val TAG = "ThreeVerticalMeasureHelper"
        private const val TOUCH_RADIUS = 80f           // 触摸检测半径
        private const val CLICK_TOLERANCE = 20f        // 点击容差
        private const val LONG_PRESS_DURATION = 800L   // 长按删除时间
        private const val DEFAULT_LINE_LENGTH = 200f   // 默认基准线长度
        private const val DEFAULT_POINT_OFFSET = 100f  // 默认独立点偏移距离
    }

    // 核心数据
    private val measurements = mutableListOf<ThreeVerticalMeasurement>()
    private lateinit var imageView: TpImageView
    private lateinit var bitmap: Bitmap
    private var isInitialized = false

    // 交互状态管理
    private var selectedMeasurement: ThreeVerticalMeasurement? = null
    private var isDraggingPoint = false
    private var draggedPointIndex = -1
    private var longPressStartTime = 0L
    private var lastTouchX = 0f
    private var lastTouchY = 0f

    // 回调函数
    private var measurementUpdateCallback: (() -> Unit)? = null

    /**
     * 🚀 初始化助手
     */
    fun init(imageView: TpImageView, bitmap: Bitmap) {
        this.imageView = imageView
        this.bitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
        this.isInitialized = true
        
        Log.d(TAG, "🚀 ThreeVerticalMeasureHelper initialized")
    }

    /**
     * 🔄 设置测量更新回调
     */
    fun setMeasurementUpdateCallback(callback: () -> Unit) {
        this.measurementUpdateCallback = callback
    }

    /**
     * 📏 开始新的三垂直测量 - 在屏幕中心创建默认测量
     */
    fun startNewMeasurement(): String {
        if (!isInitialized) {
            Log.e(TAG, "❌ Helper not initialized")
            return ""
        }

        try {
            Log.d(TAG, "📏 Creating new three vertical measurement")

            // 计算屏幕中心位置
            val centerX = imageView.width / 2f
            val centerY = imageView.height / 2f

            // 创建三垂直测量实例
            val measurement = ThreeVerticalMeasurement().apply {
                // 设置视图坐标：基准线两端点 + 独立点
                viewPoints.add(PointF(centerX - DEFAULT_LINE_LENGTH / 2, centerY)) // 基准线起点
                viewPoints.add(PointF(centerX + DEFAULT_LINE_LENGTH / 2, centerY)) // 基准线终点
                viewPoints.add(PointF(centerX, centerY - DEFAULT_POINT_OFFSET))    // 独立点

                isCompleted = true
                isSelected = true // 新创建的测量默认选中

                // 计算垂足位置
                perpendicularFoot = calculatePerpendicularFoot(imageView)
                updateTextPosition()
            }

            // 同步位图坐标
            measurement.syncBitmapCoords(imageView)

            // 取消其他测量的选中状态
            measurements.forEach {
                it.isSelected = false
                it.isEditing = false
            }

            // 添加到测量列表
            measurements.add(measurement)

            Log.d(TAG, "✅ New three vertical measurement created: ${measurement.id}")
            Log.d(TAG, "📊 Current measurements count: ${measurements.size}")

            // 触发更新回调
            measurementUpdateCallback?.invoke()

            return measurement.id

        } catch (e: Exception) {
            Log.e(TAG, "❌ Failed to create new three vertical measurement: ${e.message}")
            e.printStackTrace()
            return ""
        }
    }

    /**
     * 📏 添加新的三垂直测量（在测量模式下）
     */
    fun addNewMeasurement(): String {
        return startNewMeasurement()
    }

    /**
     * 🎯 处理触摸事件 - 三区域触摸处理逻辑
     */
    fun handleTouchEvent(event: MotionEvent, viewWidth: Int, viewHeight: Int): Boolean {
        if (!isInitialized) {
            Log.w(TAG, "⚠️ Helper not initialized, ignoring touch event")
            return false
        }

        val x = event.x
        val y = event.y
        val touchPoint = PointF(x, y)

        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                Log.d(TAG, "🎯 ACTION_DOWN at ($x, $y)")

                // 记录触摸起始信息
                longPressStartTime = System.currentTimeMillis()
                lastTouchX = x
                lastTouchY = y

                // 检查端点触摸（优先级最高）
                for (measurement in measurements.reversed()) {
                    if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                        selectedMeasurement = measurement
                        draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                        isDraggingPoint = true

                        // 设置选中状态（不设置编辑状态）
                        measurements.forEach { it.isSelected = false }
                        measurement.isSelected = true

                        Log.d(TAG, "✅ Started dragging point $draggedPointIndex of measurement ${measurement.id}")
                        measurementUpdateCallback?.invoke()
                        return true
                    }
                }

//                // 检查基准线段触摸
//                for (measurement in measurements.reversed()) {
//                    if (measurement.isPointOnBaseLine(touchPoint, TOUCH_RADIUS)) {
//                        selectedMeasurement = measurement
//                        measurements.forEach { it.isSelected = false }
//                        measurement.isSelected = true
//
//                        Log.d(TAG, "✅ Selected measurement by baseline: ${measurement.id}")
//                        measurementUpdateCallback?.invoke()
//                        return true
//                    }
//                }

                // 🔧 智能空白区域检测 - 避免UI按钮区域被误判 - 与LineMeasureHelper保持一致
                if (isInImageContentArea(touchPoint, viewWidth, viewHeight)) {
                    measurements.forEach { it.isSelected = false }
                    selectedMeasurement = null
                    Log.d(TAG, "🔄 Cleared all selections - empty area clicked in image content")
                    measurementUpdateCallback?.invoke()
                } else {
                    Log.d(TAG, "🛡️ Clicked in UI area, preserving selection state")
                }

                return false // 让ImageView处理缩放
            }

            MotionEvent.ACTION_MOVE -> {
                if (isDraggingPoint && selectedMeasurement != null && draggedPointIndex >= 0) {
                    // 正常拖拽处理
                    selectedMeasurement!!.updateWithConstraints(draggedPointIndex, touchPoint, imageView)
                    selectedMeasurement!!.syncBitmapCoords(imageView)
                    measurementUpdateCallback?.invoke()
                    return true
                } else if (!isDraggingPoint && selectedMeasurement != null) {
                    // 触摸恢复机制
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS) && measurement == selectedMeasurement) {
                            draggedPointIndex = measurement.getNearestPointIndex(touchPoint)
                            isDraggingPoint = true
                            Log.d(TAG, "🔄 Touch recovery successful - resumed dragging point $draggedPointIndex")

                            // 立即处理这次移动
                            selectedMeasurement!!.updateWithConstraints(draggedPointIndex, touchPoint, imageView)
                            selectedMeasurement!!.syncBitmapCoords(imageView)
                            measurementUpdateCallback?.invoke()
                            return true
                        }
                    }
                }
                return false
            }

            MotionEvent.ACTION_UP -> {
                val touchDuration = System.currentTimeMillis() - longPressStartTime
                val touchDistance = sqrt((x - lastTouchX) * (x - lastTouchX) + (y - lastTouchY) * (y - lastTouchY))
                val wasDragging = isDraggingPoint

                var handled = false

                // 重置拖拽状态
                isDraggingPoint = false
                draggedPointIndex = -1

                if (wasDragging) {
                    Log.d(TAG, "✅ Dragging completed")
                    measurementUpdateCallback?.invoke()
                    handled = true
                }

                // 长按删除 - 与LineMeasureHelper保持一致
                if (!wasDragging && touchDistance < CLICK_TOLERANCE && touchDuration > LONG_PRESS_DURATION) {
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            measurements.remove(measurement)
                            if (selectedMeasurement == measurement) {
                                selectedMeasurement = null
                            }
                            measurementUpdateCallback?.invoke()
                            Log.d(TAG, "🗑️ Long-press: Deleted measurement ${measurement.id}")
                            handled = true
                            break
                        }
                    }
                }

                // 轻触选中 - 与LineMeasureHelper保持一致
                if (!wasDragging && !handled && touchDistance < CLICK_TOLERANCE && touchDuration < LONG_PRESS_DURATION) {
                    for (measurement in measurements.reversed()) {
                        if (measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS)) {
                            measurements.forEach { it.isSelected = false }
                            measurement.isSelected = true
                            selectedMeasurement = measurement
                            measurementUpdateCallback?.invoke()
                            Log.d(TAG, "🎯 Light touch selection - selected measurement ${measurement.id}")
                            return true
                        }
                    }
                }

                return handled
            }
        }

        return false
    }

    /**
     * 🛡️ 检测触摸点是否在图像内容区域（避免UI按钮区域） - 与LineMeasureHelper保持一致
     */
    private fun isInImageContentArea(touchPoint: PointF, viewWidth: Int, viewHeight: Int): Boolean {
        try {
            // 定义UI区域边界（顶部和底部各20%为UI区域）
            val topUIHeight = viewHeight * 0.2f
            val bottomUIStart = viewHeight * 0.8f

            // 如果触摸点在顶部或底部UI区域，不取消选中
            if (touchPoint.y < topUIHeight || touchPoint.y > bottomUIStart) {
                Log.d(TAG, "🛡️ Touch in UI area: y=${touchPoint.y}, topUI=$topUIHeight, bottomUI=$bottomUIStart")
                return false
            }

            // 中间区域认为是图像内容区域
            Log.d(TAG, "📍 Touch in image content area: y=${touchPoint.y}")
            return true

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error in isInImageContentArea: ${e.message}")
            // 出错时保守处理，不清除选中状态
            return false
        }
    }

    /**
     * 📊 获取所有测量数据（用于渲染）
     */
    fun getAllMeasurementData(): List<ThreeVerticalMeasurementData> {
        return measurements.map { measurement ->
            ThreeVerticalMeasurementData(
                id = measurement.id,
                viewPoints = measurement.viewPoints.toList(),
                bitmapPoints = measurement.bitmapPoints.toList(),
                isSelected = measurement.isSelected,
                isEditing = measurement.isEditing,
                isCompleted = measurement.isCompleted,
                textPosition = measurement.textPosition,
                perpendicularFoot = measurement.perpendicularFoot,
                distance = measurement.calculatePerpendicularDistance()
            )
        }
    }

    /**
     * 🔄 缩放变化时的坐标同步
     */
    fun onScaleChanged() {
        if (!isInitialized) return

        Log.d(TAG, "🔄 Scale changed, syncing coordinates for ${measurements.size} measurements")

        measurements.forEach { measurement ->
            measurement.syncViewCoords(imageView)
        }

        measurementUpdateCallback?.invoke()
    }

    /**
     * 🎯 检查是否有点在测量上
     */
    fun isPointOnMeasurement(touchPoint: PointF): Boolean {
        return measurements.any { measurement ->
            measurement.isPointInTouchRange(touchPoint, TOUCH_RADIUS) ||
            measurement.isPointOnBaseLine(touchPoint, TOUCH_RADIUS)
        }
    }

    /**
     * 🎯 检查是否靠近任何测量
     */
    fun isNearAnyMeasurement(touchPoint: PointF): Boolean {
        return isPointOnMeasurement(touchPoint)
    }

    /**
     * 🎯 检查是否正在拖拽点
     */
    fun isDraggingPoint(): Boolean {
        return isDraggingPoint
    }

    /**
     * 🎯 检查是否有选中的测量
     */
    fun hasSelectedMeasurement(): Boolean {
        return measurements.any { it.isSelected }
    }

    /**
     * 🎯 获取测量数量
     */
    fun getMeasurementCount(): Int = measurements.size

    /**
     * 📊 获取所有测量
     */
    fun getAllMeasurements(): List<ThreeVerticalMeasurement> = measurements.toList()

    /**
     * 🔄 设置选中的测量
     */
    fun setSelectedMeasurement(measurement: ThreeVerticalMeasurement) {
        // 确保测量在列表中
        if (measurements.contains(measurement)) {
            // 清除其他测量的选中状态
            measurements.forEach { it.isSelected = false }
            // 设置新的选中测量
            measurement.isSelected = true
            selectedMeasurement = measurement
            Log.d(TAG, "🔄 Selected measurement set: ${measurement.id}")
            measurementUpdateCallback?.invoke()
        } else {
            Log.w(TAG, "⚠️ Cannot select measurement not in list: ${measurement.id}")
        }
    }

    /**
     * 🔄 清除所有选中状态
     */
    fun clearSelection() {
        Log.d(TAG, "🔄 Clearing all selections - caaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaurrent selectedMeasurement: ${selectedMeasurement?.id}")
        measurements.forEach { it.isSelected = false }
        selectedMeasurement = null
        measurementUpdateCallback?.invoke()
        Log.d(TAG, "🔄 All selections cleared")
    }

    /**
     * 🗑️ 删除选中的测量 - 与LineMeasureHelper保持一致
     */
    fun deleteSelectedMeasurement(): Boolean {
        val selectedIndex = measurements.indexOfFirst { it.isSelected }
        return if (selectedIndex >= 0) {
            val deletedMeasurement = measurements.removeAt(selectedIndex)
            selectedMeasurement = null
            // 注意：ThreeVerticalMeasureHelper没有activeMeasurement字段，所以不需要清理
            resetInteractionState()
            measurementUpdateCallback?.invoke()
            Log.d(TAG, "🗑️ Deleted selected three vertical measurement at index $selectedIndex: ${deletedMeasurement.id}")
            true
        } else {
            Log.w(TAG, "⚠️ No selected three vertical measurement to delete")
            false
        }
    }

    /**
     * 🔄 重置交互状态
     */
    private fun resetInteractionState() {
        isDraggingPoint = false
        draggedPointIndex = -1
        longPressStartTime = 0L
        lastTouchX = 0f
        lastTouchY = 0f
    }

    /**
     * 🧹 清理资源
     */
    fun cleanup() {
        measurements.clear()
        selectedMeasurement = null
        resetInteractionState()
        measurementUpdateCallback = null
        isInitialized = false

        Log.d(TAG, "🧹 ThreeVerticalMeasureHelper cleaned up")
    }
}
